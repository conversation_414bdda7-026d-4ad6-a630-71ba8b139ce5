# Archie Service Backend - System Architecture Documentation

This document provides a comprehensive overview of the Archie Service Backend system architecture, including class relationships, service interactions, API endpoints, and data flow diagrams.

## Table of Contents

1. [System Overview](#system-overview)
2. [High-Level Architecture](#high-level-architecture)
3. [Database Schema & Models](#database-schema--models)
4. [Service Layer Architecture](#service-layer-architecture)
5. [API Endpoints Structure](#api-endpoints-structure)
6. [Authentication & Authorization Flow](#authentication--authorization-flow)
7. [Project Workflow](#project-workflow)
8. [Code Generation Flow](#code-generation-flow)

## System Overview

Archie Service Backend is a Flask-based microservice that provides APIs for project management, technical specification generation, and code generation. The system integrates with Firebase for authentication, GitHub for repository management, and various cloud services.

## High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Frontend]
        MOBILE[Mobile App]
        API_CLIENT[API Clients]
    end

    subgraph "API Gateway"
        FLASK[Flask Application]
        CORS[CORS Middleware]
        AUTH_MW[Auth Middleware]
    end

    subgraph "Service Layer"
        USER_SVC[User Service]
        PROJECT_SVC[Project Service]
        TECH_SPEC_SVC[Tech Spec Service]
        CODE_GEN_SVC[Code Gen Service]
        SUBSCRIPTION_SVC[Subscription Service]
        GITHUB_SVC[GitHub Service]
    end

    subgraph "External Services"
        FIREBASE[Firebase Auth]
        GITHUB[GitHub API]
        STRIPE[Stripe]
        HUBSPOT[HubSpot]
        GCS[Google Cloud Storage]
        PUBSUB[Google Pub/Sub]
    end

    subgraph "Database"
        POSTGRES[(PostgreSQL)]
    end

    WEB --> FLASK
    MOBILE --> FLASK
    API_CLIENT --> FLASK

    FLASK --> CORS
    CORS --> AUTH_MW
    AUTH_MW --> USER_SVC
    AUTH_MW --> PROJECT_SVC
    AUTH_MW --> TECH_SPEC_SVC
    AUTH_MW --> CODE_GEN_SVC

    USER_SVC --> FIREBASE
    USER_SVC --> POSTGRES
    USER_SVC --> SUBSCRIPTION_SVC

    PROJECT_SVC --> POSTGRES
    PROJECT_SVC --> GITHUB_SVC

    TECH_SPEC_SVC --> POSTGRES
    TECH_SPEC_SVC --> GCS
    TECH_SPEC_SVC --> PUBSUB

    CODE_GEN_SVC --> POSTGRES
    CODE_GEN_SVC --> GITHUB
    CODE_GEN_SVC --> GCS

    SUBSCRIPTION_SVC --> STRIPE
    SUBSCRIPTION_SVC --> POSTGRES

    GITHUB_SVC --> GITHUB
```

## Database Schema & Models

```mermaid
erDiagram
    User ||--o{ Project : owns
    User ||--|| Subscription : has
    User ||--|| UserConfig : has
    User ||--o{ GeoLocation : has

    Project ||--o{ TechnicalSpec : contains
    Project ||--o{ CodeGeneration : generates
    Project ||--o{ SoftwareRequirement : has
    Project ||--o{ GitHubProjectRepo : linked_to
    Project ||--o{ Job : creates

    TechnicalSpec ||--o{ CodeGeneration : generates
    TechnicalSpec ||--o{ ProjectRun : executes

    CodeGeneration ||--o{ BlitzyCommit : contains

    Subscription ||--|| SubscriptionType : type

    User {
        string id PK
        string user_id UK
        string email UK
        string first_name
        string last_name
        boolean is_verified
        boolean registration_completed
        datetime created_at
        datetime updated_at
    }

    Project {
        string id PK
        string user_id FK
        string name
        string description
        string status
        datetime created_at
        datetime updated_at
    }

    TechnicalSpec {
        string id PK
        string project_id FK
        string content
        string status
        datetime created_at
        datetime updated_at
    }

    CodeGeneration {
        string id PK
        string project_id FK
        string tech_spec_id FK
        string status
        string output_path
        datetime created_at
        datetime updated_at
    }

    Subscription {
        string id PK
        string user_id FK
        string plan_name
        datetime start_date
        datetime end_date
        string status
    }

    UserConfig {
        string id PK
        string user_id FK
        boolean tech_spec_notification_enabled
        boolean code_gen_notification_enabled
        json platform_config
    }
```

## Service Layer Architecture

```mermaid
graph TB
    subgraph "API Routes"
        USER_ROUTE["/v1/user"]
        PROJECT_ROUTE["/v1/project"]
        TECH_SPEC_ROUTE["/v1/project/{id}/tech-spec"]
        CODE_GEN_ROUTE["/v1/project/{id}/code-gen"]
        AUTH_ROUTE["/v1/auth"]
        GITHUB_ROUTE["/v1/github"]
        STRIPE_ROUTE["/v1/stripe"]
    end

    subgraph "Service Layer"
        USER_SERVICE[user_service.py]
        PROJECT_SERVICE[project_service.py]
        TECH_SPEC_SERVICE[tech_spec_service.py]
        CODE_GEN_SERVICE[code_gen_service.py]
        SUBSCRIPTION_SERVICE[subscription_service.py]
        GITHUB_SERVICE[github_service.py]
        IP_SERVICE[ip_service.py]
        EMAIL_SERVICE[email_validation_service.py]
    end

    subgraph "Utilities"
        AUTH_UTILS[auth_utils.py]
        FIREBASE_UTILS[firebase_utils.py]
        PROJECT_UTILS[project_utils.py]
        TECH_SPEC_UTILS[tech_spec_utils.py]
    end

    subgraph "Database Layer"
        DB_CLIENT[db_client]
        MODELS[common_models]
    end

    USER_ROUTE --> USER_SERVICE
    PROJECT_ROUTE --> PROJECT_SERVICE
    TECH_SPEC_ROUTE --> TECH_SPEC_SERVICE
    CODE_GEN_ROUTE --> CODE_GEN_SERVICE
    AUTH_ROUTE --> AUTH_UTILS
    GITHUB_ROUTE --> GITHUB_SERVICE
    STRIPE_ROUTE --> SUBSCRIPTION_SERVICE

    USER_SERVICE --> DB_CLIENT
    PROJECT_SERVICE --> DB_CLIENT
    TECH_SPEC_SERVICE --> DB_CLIENT
    CODE_GEN_SERVICE --> DB_CLIENT
    SUBSCRIPTION_SERVICE --> DB_CLIENT
    GITHUB_SERVICE --> DB_CLIENT

    USER_SERVICE --> FIREBASE_UTILS
    PROJECT_SERVICE --> PROJECT_UTILS
    TECH_SPEC_SERVICE --> TECH_SPEC_UTILS

    DB_CLIENT --> MODELS
```

## API Endpoints Structure

```mermaid
graph TB
    subgraph AUTH["Authentication Endpoints"]
        AUTH_POST["POST /v1/auth"]
        AUTH_REFRESH["POST /v1/auth/refresh"]
    end

    subgraph USER["User Management"]
        USER_CREATE["POST /v1/user"]
        USER_REGISTER["POST /v1/user/register"]
        USER_PROFILE["GET /v1/user/profile"]
        USER_UPDATE["PUT /v1/user/profile"]
        USER_CONFIG["PUT /v1/user/config"]
        USER_GITHUB["POST /v1/user/github"]
    end

    subgraph PROJECT["Project Management"]
        PROJECT_CREATE["POST /v1/project"]
        PROJECT_LIST["GET /v1/project"]
        PROJECT_GET["GET /v1/project/id"]
        PROJECT_UPDATE["PUT /v1/project/id"]
        PROJECT_DELETE["DELETE /v1/project/id"]
    end

    subgraph TECHSPEC["Technical Specification"]
        TECH_SPEC_CREATE["POST /v1/project/id/tech-spec"]
        TECH_SPEC_GET["GET /v1/project/id/tech-spec"]
        TECH_SPEC_UPDATE["PUT /v1/project/id/tech-spec"]
        TECH_SPEC_PROMPT["POST /v1/project/id/tech-spec/prompt"]
        TECH_SPEC_REVERT["POST /v1/project/id/tech-spec/revert"]
    end

    subgraph CODEGEN["Code Generation"]
        CODE_GEN_CREATE["POST /v1/project/id/code-gen"]
        CODE_GEN_GET["GET /v1/project/id/code-gen"]
        CODE_GEN_STATUS["GET /v1/project/id/code-gen/status"]
        CODE_GEN_DOWNLOAD["GET /v1/project/id/code-gen/download"]
    end

    subgraph GITHUB["GitHub Integration"]
        GITHUB_REPOS["GET /v1/project/id/github/repos"]
        GITHUB_CONNECT["POST /v1/project/id/github/connect"]
        GITHUB_BRANCHES["GET /v1/project/id/github/branches"]
    end
```

## Authentication & Authorization Flow

```mermaid
sequenceDiagram
    participant Client
    participant Flask
    participant AuthMiddleware
    participant Firebase
    participant UserService
    participant Database

    Client->>Flask: Request with ID Token
    Flask->>AuthMiddleware: Validate Request
    AuthMiddleware->>Firebase: Verify ID Token
    Firebase-->>AuthMiddleware: Token Valid/User Info

    alt Token Valid
        AuthMiddleware->>UserService: Get/Create User
        UserService->>Database: Query User

        alt User Exists
            Database-->>UserService: User Data
        else User Not Found
            UserService->>Firebase: Get User Details
            Firebase-->>UserService: User Details
            UserService->>Database: Create User
            Database-->>UserService: Created User
        end

        UserService-->>AuthMiddleware: User Info
        AuthMiddleware->>Flask: Continue Request
        Flask-->>Client: API Response
    else Token Invalid
        AuthMiddleware-->>Client: 401 Unauthorized
    end
```

## Project Workflow

```mermaid
stateDiagram-v2
    [*] --> ProjectCreated
    ProjectCreated --> RequirementsGathering
    RequirementsGathering --> TechSpecGeneration
    TechSpecGeneration --> TechSpecReview
    TechSpecReview --> TechSpecApproved
    TechSpecReview --> TechSpecRevision
    TechSpecRevision --> TechSpecGeneration
    TechSpecApproved --> CodeGeneration
    CodeGeneration --> CodeReview
    CodeReview --> CodeApproved
    CodeReview --> CodeRevision
    CodeRevision --> CodeGeneration
    CodeApproved --> GitHubIntegration
    GitHubIntegration --> ProjectCompleted
    ProjectCompleted --> [*]

    note right of TechSpecGeneration
        AI generates technical
        specifications based on
        requirements
    end note

    note right of CodeGeneration
        AI generates code based on
        approved technical specs
    end note
```

## Code Generation Flow

```mermaid
sequenceDiagram
    participant User
    participant API
    participant TechSpecService
    participant CodeGenService
    participant PubSub
    participant GCS
    participant GitHub

    User->>API: POST /project/{id}/code-gen
    API->>TechSpecService: Get Latest Tech Spec
    TechSpecService-->>API: Tech Spec Data

    API->>CodeGenService: Create Code Generation Job
    CodeGenService->>PubSub: Publish Generation Request
    CodeGenService-->>API: Job Created
    API-->>User: 201 Created

    PubSub->>CodeGenService: Process Generation
    CodeGenService->>GCS: Store Generated Code
    CodeGenService->>GitHub: Create/Update Repository
    GitHub-->>CodeGenService: Repository Updated
    CodeGenService->>API: Update Job Status

    User->>API: GET /project/{id}/code-gen/status
    API-->>User: Generation Status
```

## Service Dependencies

```mermaid
graph TD
    subgraph "Core Services"
        USER_SVC[User Service]
        PROJECT_SVC[Project Service]
        TECH_SPEC_SVC[Tech Spec Service]
        CODE_GEN_SVC[Code Gen Service]
    end

    subgraph "Supporting Services"
        SUBSCRIPTION_SVC[Subscription Service]
        GITHUB_SVC[GitHub Service]
        IP_SVC[IP Service]
        EMAIL_SVC[Email Service]
        CONFIG_SVC[User Config Service]
    end

    subgraph "External Dependencies"
        FIREBASE[Firebase Auth]
        STRIPE[Stripe API]
        GITHUB_API[GitHub API]
        HUBSPOT[HubSpot API]
        GCS[Google Cloud Storage]
        PUBSUB[Google Pub/Sub]
    end

    USER_SVC --> SUBSCRIPTION_SVC
    USER_SVC --> CONFIG_SVC
    USER_SVC --> IP_SVC
    USER_SVC --> EMAIL_SVC
    USER_SVC --> FIREBASE

    PROJECT_SVC --> USER_SVC
    PROJECT_SVC --> GITHUB_SVC

    TECH_SPEC_SVC --> PROJECT_SVC
    TECH_SPEC_SVC --> GCS
    TECH_SPEC_SVC --> PUBSUB

    CODE_GEN_SVC --> TECH_SPEC_SVC
    CODE_GEN_SVC --> GITHUB_SVC
    CODE_GEN_SVC --> GCS

    SUBSCRIPTION_SVC --> STRIPE
    GITHUB_SVC --> GITHUB_API
    EMAIL_SVC --> HUBSPOT
```

## Key Classes and Methods

### User Service (`user_service.py`)

-   `create_user_using_firebase_user_id()` - Creates user with Firebase integration
-   `get_user_by_id()` - Retrieves user with subscription and config
-   `get_user_by_user_email()` - Finds user by email
-   `fetch_user_info_from_auth_provider()` - Firebase user lookup

### Project Service (`project_service.py`)

-   `get_project_by_user_id_with_relations()` - Complete project data
-   `get_all_projects_by_user_id()` - User's project list
-   `save_project()` - Create/update project
-   `update_project_timeline()` - Project milestone tracking

### Tech Spec Service (`tech_spec_service.py`)

-   `create_tech_spec()` - Generate technical specifications
-   `get_tech_spec_by_project_id()` - Retrieve specifications
-   `update_tech_spec_status()` - Status management
-   `revert_tech_spec()` - Version control

### Code Generation Service (`code_gen_service.py`)

-   `create_code_generation()` - Initiate code generation
-   `get_code_gen_by_project_id()` - Retrieve generation status
-   `update_code_gen_status()` - Progress tracking
-   `download_generated_code()` - Code delivery

---

## TODO Items

-   [ ] Add caching layer for frequently accessed data (Redis)
-   [ ] Implement rate limiting for API endpoints
-   [ ] Add comprehensive logging and monitoring
-   [ ] Set up automated testing pipeline
-   [ ] Add API documentation with Swagger/OpenAPI
-   [ ] Implement webhook notifications for async operations
-   [ ] Add data backup and disaster recovery procedures

```

```
