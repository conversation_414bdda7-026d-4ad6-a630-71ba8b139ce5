# Archie Service Backend Documentation

Welcome to the Archie Service Backend documentation. This directory contains comprehensive documentation about the system architecture, design patterns, and implementation details.

## Documentation Files

### 📋 [System Architecture](./system-architecture.md)
Complete system architecture documentation including:
- **High-level architecture diagrams** showing the overall system structure
- **Database schema and relationships** with detailed ER diagrams
- **Service layer architecture** and dependencies
- **API endpoints structure** and organization
- **Authentication & authorization flow** with sequence diagrams
- **Project workflow** state diagrams
- **Code generation flow** and process
- **Service dependencies** and external integrations

## Diagram Types Used

The documentation uses **Mermaid diagrams** for visual representation:

- 🏗️ **Architecture Diagrams** - System components and their relationships
- 🗄️ **Entity Relationship Diagrams** - Database schema and model relationships  
- 🔄 **Sequence Diagrams** - Process flows and interactions
- 📊 **State Diagrams** - Workflow and status transitions
- 🌐 **Graph Diagrams** - Service dependencies and API structure

## How to View Diagrams

The Mermaid diagrams can be viewed in:
- **GitHub** - Native Mermaid support in markdown files
- **VS Code** - With Mermaid preview extensions
- **Mermaid Live Editor** - Copy diagram code to [mermaid.live](https://mermaid.live)
- **Documentation sites** - Most modern documentation platforms support Mermaid

## Key System Components

### Core Services
- **User Service** - User management and authentication
- **Project Service** - Project lifecycle management
- **Tech Spec Service** - Technical specification generation
- **Code Generation Service** - AI-powered code generation

### External Integrations
- **Firebase** - Authentication and user management
- **GitHub** - Repository management and code hosting
- **Stripe** - Payment processing and subscriptions
- **Google Cloud** - Storage, Pub/Sub, and infrastructure
- **HubSpot** - Customer relationship management

### Database Models
- **User** - User accounts and profiles
- **Project** - Development projects
- **TechnicalSpec** - AI-generated specifications
- **CodeGeneration** - Generated code artifacts
- **Subscription** - User subscription management

## API Structure

The API follows RESTful conventions with the following main endpoints:
- `/v1/auth` - Authentication and token management
- `/v1/user` - User management and profiles
- `/v1/project` - Project CRUD operations
- `/v1/project/{id}/tech-spec` - Technical specification management
- `/v1/project/{id}/code-gen` - Code generation operations
- `/v1/github` - GitHub integration endpoints

## Development Notes

### TODO Items
- Add caching layer for performance optimization
- Implement comprehensive rate limiting
- Enhance logging and monitoring capabilities
- Set up automated testing pipeline
- Add API documentation with OpenAPI/Swagger
- Implement webhook notifications for async operations

### Architecture Principles
- **Microservice-oriented** design with clear service boundaries
- **Event-driven** architecture using Google Pub/Sub
- **Database-per-service** pattern with shared common models
- **API-first** approach with consistent REST conventions
- **Cloud-native** deployment on Google Cloud Platform

---

For detailed technical information, please refer to the [System Architecture](./system-architecture.md) document.
