# Design Document: Distributed Idempotency for Tech Spec API

## 1. Problem Statement

### Current Implementation Issues
Our current idempotency solution uses **local threading locks** which have critical limitations:

```python
# Current approach - ONLY works in single-instance deployments
project_locks = {"add_feature": {}, "refactor_code": {}, "sync_tech_spec": {}}
locks_mutex = threading.Lock()

def get_project_lock(project_id: str, job_type: str) -> threading.Lock:
    with locks_mutex:
        if project_id not in project_locks[job_type]:
            project_locks[job_type][project_id] = threading.Lock()
        return project_locks[job_type][project_id]
```

### Critical Problems:
1. **Race conditions in distributed environments** - Multiple API instances can create duplicate jobs
2. **No cross-instance coordination** - Locks are local to each process/container
3. **Scalability bottleneck** - Cannot horizontally scale the API service
4. **Data inconsistency** - Multiple jobs of same type can run simultaneously across instances

## 2. Requirements

### Functional Requirements
- **FR1**: Ensure only one job of each type (ADD_FEATURE, REFACTOR_CODE, SYNC_TECH_SPEC) per project across all API instances
- **FR2**: Handle concurrent requests from multiple API instances gracefully
- **FR3**: Provide clear error messages when duplicate jobs are attempted
- **FR4**: Support horizontal scaling of API service

### Non-Functional Requirements
- **NFR1**: Lock acquisition latency < 100ms
- **NFR2**: 99.9% availability for lock service
- **NFR3**: Handle network partitions gracefully
- **NFR4**: Automatic lock cleanup for failed operations
- **NFR5**: Backward compatibility with existing API contracts

## 3. Solution Options

### Option 1: Distributed Locks with Redis

#### Architecture
```mermaid
graph TB
    subgraph "Load Balancer"
        LB[Load Balancer]
    end
    
    subgraph "API Instances"
        API1[API Instance 1]
        API2[API Instance 2]
        API3[API Instance N]
    end
    
    subgraph "Shared State"
        REDIS[(Redis Cluster)]
        DB[(PostgreSQL)]
    end
    
    LB --> API1
    LB --> API2
    LB --> API3
    
    API1 --> REDIS
    API2 --> REDIS
    API3 --> REDIS
    
    API1 --> DB
    API2 --> DB
    API3 --> DB
```

#### Implementation Details
```python
import redis
import uuid
import time
from contextlib import contextmanager

class DistributedLockManager:
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
        self.lock_timeout = 300  # 5 minutes
        self.acquire_timeout = 10  # 10 seconds
    
    @contextmanager
    def acquire_project_lock(self, project_id: str, job_type: str):
        lock_key = f"tech_spec_lock:{project_id}:{job_type}"
        lock_value = str(uuid.uuid4())
        
        try:
            # Try to acquire lock with timeout
            if self._acquire_lock(lock_key, lock_value):
                yield
            else:
                raise JobAlreadyRunningError(
                    f"Cannot submit {job_type} request while another {job_type} job is in progress."
                )
        finally:
            self._release_lock(lock_key, lock_value)
    
    def _acquire_lock(self, key: str, value: str) -> bool:
        # Use SET with NX (only if not exists) and EX (expiration)
        return self.redis.set(key, value, nx=True, ex=self.lock_timeout)
    
    def _release_lock(self, key: str, value: str):
        # Lua script for atomic check-and-delete
        lua_script = """
        if redis.call("GET", KEYS[1]) == ARGV[1] then
            return redis.call("DEL", KEYS[1])
        else
            return 0
        end
        """
        self.redis.eval(lua_script, 1, key, value)
```

#### Pros:
- ✅ True distributed coordination
- ✅ Fast lock acquisition (< 10ms typically)
- ✅ Automatic expiration prevents deadlocks
- ✅ Battle-tested in production environments
- ✅ Minimal code changes required

#### Cons:
- ❌ Additional infrastructure dependency (Redis)
- ❌ Network calls add latency
- ❌ Potential single point of failure (mitigated with Redis Cluster)

### Option 2: Database-Based Distributed Locks

#### Implementation Details
```python
from sqlalchemy import text
from contextlib import contextmanager

class DatabaseLockManager:
    def __init__(self, db_session):
        self.session = db_session
    
    @contextmanager
    def acquire_project_lock(self, project_id: str, job_type: str):
        lock_name = f"tech_spec_{project_id}_{job_type}"
        
        try:
            # PostgreSQL advisory locks
            result = self.session.execute(
                text("SELECT pg_try_advisory_lock(:lock_id)"),
                {"lock_id": hash(lock_name) % (2**31)}
            ).scalar()
            
            if not result:
                raise JobAlreadyRunningError(
                    f"Cannot submit {job_type} request while another {job_type} job is in progress."
                )
            
            yield
            
        finally:
            self.session.execute(
                text("SELECT pg_advisory_unlock(:lock_id)"),
                {"lock_id": hash(lock_name) % (2**31)}
            )
```

#### Pros:
- ✅ No additional infrastructure
- ✅ ACID guarantees
- ✅ Automatic cleanup on connection close

#### Cons:
- ❌ Database connection overhead
- ❌ Limited to PostgreSQL advisory locks
- ❌ Potential database performance impact

### Option 3: Message Queue with Idempotency Keys

#### Architecture
```mermaid
graph TB
    subgraph "API Layer"
        API1[API Instance 1]
        API2[API Instance 2]
        API3[API Instance N]
    end
    
    subgraph "Message Queue"
        QUEUE[Redis/RabbitMQ Queue]
    end
    
    subgraph "Workers"
        W1[Worker 1]
        W2[Worker 2]
        W3[Worker N]
    end
    
    subgraph "Storage"
        DB[(PostgreSQL)]
        CACHE[(Redis Cache)]
    end
    
    API1 --> QUEUE
    API2 --> QUEUE
    API3 --> QUEUE
    
    QUEUE --> W1
    QUEUE --> W2
    QUEUE --> W3
    
    W1 --> DB
    W2 --> DB
    W3 --> DB
    
    W1 --> CACHE
    W2 --> CACHE
    W3 --> CACHE
```

#### Implementation Details
```python
import hashlib
from dataclasses import dataclass
from typing import Dict, Any

@dataclass
class TechSpecJobRequest:
    project_id: str
    job_type: str
    user_info: Dict[str, Any]
    payload: Any
    idempotency_key: str

class IdempotentJobProcessor:
    def __init__(self, redis_client, queue_client):
        self.redis = redis_client
        self.queue = queue_client
    
    def submit_job(self, request: TechSpecJobRequest):
        # Generate idempotency key
        idempotency_key = self._generate_idempotency_key(
            request.project_id, request.job_type
        )
        
        # Check if job already exists
        existing_job = self.redis.get(f"job:{idempotency_key}")
        if existing_job:
            raise JobAlreadyRunningError(
                f"Job {request.job_type} already in progress for project {request.project_id}"
            )
        
        # Mark job as in progress
        self.redis.setex(f"job:{idempotency_key}", 3600, "in_progress")
        
        # Queue the job
        self.queue.enqueue(self._process_job, request)
    
    def _generate_idempotency_key(self, project_id: str, job_type: str) -> str:
        return hashlib.sha256(f"{project_id}:{job_type}".encode()).hexdigest()
    
    def _process_job(self, request: TechSpecJobRequest):
        try:
            # Process the actual job
            self._execute_tech_spec_job(request)
            
            # Mark as completed
            self.redis.setex(f"job:{request.idempotency_key}", 86400, "completed")
        except Exception as e:
            # Mark as failed and allow retry
            self.redis.delete(f"job:{request.idempotency_key}")
            raise
```

#### Pros:
- ✅ Natural idempotency through message deduplication
- ✅ Async processing improves API response times
- ✅ Built-in retry mechanisms
- ✅ Better observability and monitoring

#### Cons:
- ❌ Significant architectural changes required
- ❌ Eventual consistency model
- ❌ More complex error handling

## 4. Recommended Solution: Redis Distributed Locks

### Rationale
After evaluating all options, **Redis Distributed Locks (Option 1)** is recommended because:

1. **Minimal disruption**: Requires least changes to existing codebase
2. **Performance**: Sub-10ms lock acquisition in most cases
3. **Reliability**: Proven in production at scale
4. **Simplicity**: Easy to understand and debug
5. **Flexibility**: Can be extended for other use cases

## 5. Implementation Plan

### Phase 1: Infrastructure Setup
```yaml
# docker-compose.yml addition
redis:
  image: redis:7-alpine
  ports:
    - "6379:6379"
  command: redis-server --appendonly yes
  volumes:
    - redis_data:/data

# For production: Redis Cluster
redis-cluster:
  image: redis:7-alpine
  deploy:
    replicas: 6
  environment:
    - REDIS_CLUSTER_ENABLED=yes
```

### Phase 2: Lock Manager Implementation
```python
# src/infrastructure/distributed_lock.py
import redis
import uuid
import time
from contextlib import contextmanager
from typing import Generator
from src.config import REDIS_URL, REDIS_LOCK_TIMEOUT

class DistributedLockManager:
    def __init__(self):
        self.redis = redis.from_url(REDIS_URL, decode_responses=True)
        self.lock_timeout = REDIS_LOCK_TIMEOUT

    @contextmanager
    def acquire_project_lock(self, project_id: str, job_type: str) -> Generator[None, None, None]:
        lock_key = f"tech_spec_lock:{project_id}:{job_type}"
        lock_value = str(uuid.uuid4())

        try:
            if self._acquire_lock_with_retry(lock_key, lock_value):
                yield
            else:
                raise JobAlreadyRunningError(
                    f"Cannot submit {job_type} request while another {job_type} job is in progress."
                )
        finally:
            self._release_lock(lock_key, lock_value)

    def _acquire_lock_with_retry(self, key: str, value: str, max_retries: int = 3) -> bool:
        for attempt in range(max_retries):
            if self.redis.set(key, value, nx=True, ex=self.lock_timeout):
                return True
            time.sleep(0.1 * (2 ** attempt))  # Exponential backoff
        return False

    def _release_lock(self, key: str, value: str):
        lua_script = """
        if redis.call("GET", KEYS[1]) == ARGV[1] then
            return redis.call("DEL", KEYS[1])
        else
            return 0
        end
        """
        self.redis.eval(lua_script, 1, key, value)

# Singleton instance
distributed_lock_manager = DistributedLockManager()
```

### Phase 3: Update Existing Code
```python
# src/api/routes/tech_spec.py
from src.infrastructure.distributed_lock import distributed_lock_manager

# Replace existing lock usage
def handle_tech_spec_job_request(user_info: Dict[str, Any], project_info: Project, payload: TechSpecPromptInput):
    if payload.type == TechSpecJobTypeModel.ADD_FEATURE:
        validate_existing_project_job_type(project_info, payload)
        with distributed_lock_manager.acquire_project_lock(project_info.id, "add_feature"):
            is_tech_spec_job_type_in_progress(project_info, TechSpecJobType.ADD_FEATURE)
            tech_spec_handle_job_type_add_feature(user_info, project_info, payload)
    # ... similar for other job types
```

### Phase 4: Configuration
```python
# src/config.py
import os

REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
REDIS_LOCK_TIMEOUT = int(os.getenv("REDIS_LOCK_TIMEOUT", "300"))  # 5 minutes
REDIS_LOCK_RETRY_ATTEMPTS = int(os.getenv("REDIS_LOCK_RETRY_ATTEMPTS", "3"))
```

## 6. Monitoring and Observability

### Metrics to Track
```python
# src/infrastructure/metrics.py
from prometheus_client import Counter, Histogram, Gauge

lock_acquisitions = Counter('tech_spec_lock_acquisitions_total',
                           ['project_id', 'job_type', 'status'])
lock_duration = Histogram('tech_spec_lock_duration_seconds',
                         ['project_id', 'job_type'])
active_locks = Gauge('tech_spec_active_locks',
                    ['job_type'])
```

### Health Checks
```python
# src/api/routes/health.py
@health_bp.route("/redis", methods=["GET"])
def redis_health():
    try:
        distributed_lock_manager.redis.ping()
        return {"status": "healthy", "redis": "connected"}, 200
    except Exception as e:
        return {"status": "unhealthy", "redis": str(e)}, 503
```

## 7. Error Handling and Fallback

### Graceful Degradation
```python
class DistributedLockManager:
    def __init__(self):
        self.redis = redis.from_url(REDIS_URL, decode_responses=True)
        self.fallback_enabled = os.getenv("LOCK_FALLBACK_ENABLED", "true").lower() == "true"

    @contextmanager
    def acquire_project_lock(self, project_id: str, job_type: str):
        try:
            # Try distributed lock first
            with self._acquire_distributed_lock(project_id, job_type):
                yield
        except redis.RedisError as e:
            if self.fallback_enabled:
                logger.warning(f"Redis lock failed, falling back to database check: {e}")
                # Fallback to database-only idempotency check
                is_tech_spec_job_type_in_progress(project_info, job_type)
                yield
            else:
                raise ServiceUnavailableError("Lock service unavailable")
```

## 8. Migration Strategy

### Phase 1: Parallel Deployment (Week 1-2)
- Deploy Redis infrastructure
- Implement distributed lock manager
- Add feature flag for distributed locks
- Run both systems in parallel

### Phase 2: Gradual Rollout (Week 3-4)
- Enable distributed locks for 10% of traffic
- Monitor metrics and error rates
- Gradually increase to 100%

### Phase 3: Cleanup (Week 5)
- Remove local lock implementation
- Remove feature flags
- Update documentation

## 9. Testing Strategy

### Unit Tests
```python
def test_distributed_lock_prevents_concurrent_jobs():
    lock_manager = DistributedLockManager()

    # Simulate concurrent requests
    with lock_manager.acquire_project_lock("project-1", "add_feature"):
        with pytest.raises(JobAlreadyRunningError):
            with lock_manager.acquire_project_lock("project-1", "add_feature"):
                pass

def test_lock_timeout_prevents_deadlock():
    # Test automatic lock expiration
    pass

def test_redis_failure_fallback():
    # Test graceful degradation when Redis is unavailable
    pass
```

### Integration Tests
```python
def test_cross_instance_idempotency():
    # Simulate multiple API instances
    # Verify only one job is created
    pass

def test_lock_cleanup_on_failure():
    # Verify locks are released when operations fail
    pass
```

## 10. Rollback Plan

### Immediate Rollback
- Feature flag to disable distributed locks
- Automatic fallback to database-only checks
- No data loss or corruption

### Emergency Procedures
1. Disable distributed lock feature flag
2. Scale down Redis if causing issues
3. Monitor for any stuck jobs
4. Manual cleanup if necessary

## 11. Success Metrics

### Technical Metrics
- **Lock acquisition latency**: < 100ms p99
- **Lock success rate**: > 99.9%
- **Zero duplicate jobs**: Confirmed via monitoring
- **API response time**: No degradation

### Business Metrics
- **Reduced support tickets**: Fewer duplicate job complaints
- **Improved user experience**: Consistent error messages
- **System reliability**: Higher uptime and stability

---

This design provides a robust, scalable solution for distributed idempotency while maintaining backward compatibility and providing clear migration and rollback paths.
