"""
Manual migration: update_team_table
Version: 20250624_161628_c97c00df_update_team_table
Created At: 2025-06-24T16:16:28.786437
Author: adars<PERSON>ing<PERSON><PERSON>har
"""

from typing import List, Optional
from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        # Make company_id column nullable in teammembers table
        "ALTER TABLE teams ALTER COLUMN company_id DROP NOT NULL"
    ]


def down() -> List[str]:
    return [
        # Revert company_id column to NOT NULL in teammembers table
        "ALTER TABLE teams ALTER COLUMN company_id SET NOT NULL"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
