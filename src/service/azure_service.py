import requests
from typing import Any, Dict, List, Optional
from blitzy_utils.logger import logger
from common_models.models import User

from src.error.errors import ResourceNotFound, AzureBaseError


def get_azure_organizations_by_user(user_info: User) -> List[Dict[str, Any]]:
    """
    Get Azure DevOps organizations for a user.

    This function calls the Azure DevOps REST API to get organizations
    that the user has access to.

    Args:
        user_info: User object containing user information

    Returns:
        List of Azure DevOps organizations

    Raises:
        AzureBaseError: If Azure API call fails
        ResourceNotFound: If no organizations found
    """
    try:
        # TODO: Get Azure access token for the user
        # This should be retrieved from the azure_installations table
        # or from the existing Azure authentication flow
        access_token = get_azure_access_token(user_info.id)

        if not access_token:
            raise ResourceNotFound(
                f"No Azure access token found for user {user_info.id}"
            )

        # Call Azure DevOps API to get accounts/organizations
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        # Azure DevOps API endpoint for getting accounts
        url = f"https://app.vssps.visualstudio.com/_apis/accounts?memberId={user_info.id}&api-version=6.0"

        response = requests.get(url, headers=headers)
        response.raise_for_status()

        data = response.json()
        organizations = []

        for account in data.get("value", []):
            organizations.append(
                {
                    "id": account.get("accountId"),
                    "name": account.get("accountName"),
                    "type": "Organization",
                    "url": account.get("accountUri"),
                }
            )

        logger.info(
            f"Found {len(organizations)} Azure organizations for user {user_info.id}"
        )
        return organizations

    except requests.RequestException as e:
        logger.error(
            f"Azure API error getting organizations for user {user_info.id}: {str(e)}"
        )
        raise AzureBaseError(f"Failed to get Azure organizations: {str(e)}")
    except Exception as e:
        logger.error(
            f"Unexpected error getting Azure organizations for user {user_info.id}: {str(e)}"
        )
        raise AzureBaseError(f"Unexpected error: {str(e)}")


def get_azure_repositories_by_user_and_org(
    user_info: User, org_name: str
) -> List[Dict[str, Any]]:
    """
    Get Azure DevOps repositories for a user within a specific organization.

    Args:
        user_info: User object containing user information
        org_name: Azure DevOps organization name

    Returns:
        List of Azure DevOps repositories

    Raises:
        AzureBaseError: If Azure API call fails
        ResourceNotFound: If no repositories found
    """
    try:
        access_token = get_azure_access_token(user_info.id)

        if not access_token:
            raise ResourceNotFound(
                f"No Azure access token found for user {user_info.id}"
            )

        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        # First get all projects in the organization
        projects_url = (
            f"https://dev.azure.com/{org_name}/_apis/projects?api-version=6.0"
        )
        projects_response = requests.get(projects_url, headers=headers)
        projects_response.raise_for_status()

        projects_data = projects_response.json()
        repositories = []

        # For each project, get its repositories
        for project in projects_data.get("value", []):
            project_id = project.get("id")
            project_name = project.get("name")

            repos_url = f"https://dev.azure.com/{org_name}/_apis/git/repositories?api-version=6.0"
            repos_response = requests.get(repos_url, headers=headers)
            repos_response.raise_for_status()

            repos_data = repos_response.json()

            for repo in repos_data.get("value", []):
                # Filter repositories by project
                if repo.get("project", {}).get("id") == project_id:
                    repositories.append(
                        {
                            "id": repo.get("id"),
                            "name": repo.get("name"),
                            "full_name": f"{org_name}/{repo.get('name')}",
                            "project": project_name,
                            "url": repo.get("webUrl"),
                            "private": not repo.get("isPublic", False),
                        }
                    )

        logger.info(
            f"Found {len(repositories)} Azure repositories for user {user_info.id} in org {org_name}"
        )
        return repositories

    except requests.RequestException as e:
        logger.error(
            f"Azure API error getting repositories for user {user_info.id} in org {org_name}: {str(e)}"
        )
        raise AzureBaseError(f"Failed to get Azure repositories: {str(e)}")
    except Exception as e:
        logger.error(
            f"Unexpected error getting Azure repositories for user {user_info.id} in org {org_name}: {str(e)}"
        )
        raise AzureBaseError(f"Unexpected error: {str(e)}")


def get_azure_branches_by_repo(
    user_info: User, org_name: str, repo_id: str
) -> List[Dict[str, Any]]:
    """
    Get Azure DevOps branches for a specific repository.

    Args:
        user_info: User object containing user information
        org_name: Azure DevOps organization name
        repo_id: Azure DevOps repository ID

    Returns:
        List of Azure DevOps branches

    Raises:
        AzureBaseError: If Azure API call fails
        ResourceNotFound: If no branches found
    """
    try:
        access_token = get_azure_access_token(user_info.id)

        if not access_token:
            raise ResourceNotFound(
                f"No Azure access token found for user {user_info.id}"
            )

        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        # Get Git refs (branches) for the repository
        refs_url = f"https://dev.azure.com/{org_name}/_apis/git/repositories/{repo_id}/refs?filter=heads/&api-version=6.0"

        response = requests.get(refs_url, headers=headers)
        response.raise_for_status()

        data = response.json()
        branches = []

        for ref in data.get("value", []):
            ref_name = ref.get("name", "")
            if ref_name.startswith("refs/heads/"):
                branch_name = ref_name.replace("refs/heads/", "")
                branches.append(
                    {
                        "name": branch_name,
                        "ref": ref_name,
                        "sha": ref.get("objectId"),
                        "protected": False,  # Azure DevOps doesn't have a simple protected flag in refs API
                    }
                )

        logger.info(
            f"Found {len(branches)} Azure branches for repo {repo_id} in org {org_name}"
        )
        return branches

    except requests.RequestException as e:
        logger.error(
            f"Azure API error getting branches for repo {repo_id} in org {org_name}: {str(e)}"
        )
        raise AzureBaseError(f"Failed to get Azure branches: {str(e)}")
    except Exception as e:
        logger.error(
            f"Unexpected error getting Azure branches for repo {repo_id} in org {org_name}: {str(e)}"
        )
        raise AzureBaseError(f"Unexpected error: {str(e)}")


def get_azure_access_token(user_id: str) -> Optional[str]:
    """
    Get Azure access token for a user.

    This function should retrieve the Azure access token from the database
    where it was stored during the Azure installation/authentication process.

    Args:
        user_id: User ID

    Returns:
        Azure access token or None if not found

    TODO: Implement actual token retrieval from database
    """
    # TODO: Implement actual token retrieval from azure_installations table
    # This should query the database for the user's Azure access token
    # that was stored during the /v1/azure/install process

    logger.warning(f"Azure access token retrieval not implemented for user {user_id}")
    return None


def fetch_azure_secret(user_id: str, org_name: str) -> Optional[str]:
    """
    Fetch Azure secret for a user and organization.

    This function is used by other parts of the system to get Azure credentials.

    Args:
        user_id: User ID
        org_name: Azure DevOps organization name

    Returns:
        Azure access token or None if not found
    """
    return get_azure_access_token(user_id)
