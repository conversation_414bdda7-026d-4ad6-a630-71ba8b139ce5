from typing import Optional

from common_models.db_client import get_db_session
from common_models.models import BlitzyCommit
from sqlalchemy.orm import Session


def get_blitzy_commit_by_repo_id_and_pr_number(repo_id, pr_number, session: Optional[Session] = None) -> BlitzyCommit:
    """
    Retrieve a BlitzyCommit from the database using repository ID and pull request number.

    :param repo_id: The ID of the repository to query.
    :param pr_number: The pull request number associated with the commit.
    :param session: An optional database session to use; if not provided,
        a new session will be created and managed within the function.
    :return: A BlitzyCommit object associated with the provided repository
        ID and pull request number, or None if not found.
    """
    with get_db_session(session) as session:
        blitzy_commit = (session.query(BlitzyCommit)
                         .filter(BlitzyCommit.repo_id == repo_id,
                                 BlitzyCommit.pr_number == pr_number)
                         .first())
        if blitzy_commit and not session:
            session.expunge(blitzy_commit)
        return blitzy_commit
