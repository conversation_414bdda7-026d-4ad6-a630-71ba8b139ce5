from blitzy_utils.common import blitzy_exponential_retry

from github import Github, GithubIntegration
from src.consts import GITHUB_APP_ID, GITHUB_PRIVATE_KEY
from src.scm_base.base_classes import BaseAppConnection


class GithubAppConnection(BaseAppConnection):
    def __init__(self):
        self.integration = GithubIntegration(GITHUB_APP_ID, GITHUB_PRIVATE_KEY)

    @blitzy_exponential_retry()
    def get_client(self, installation_id):
        """
        Retrieves a GitHub client instance using the installation access token.

        :param installation_id: Identifier of the GitHub App installation
        :type installation_id: int
        :return: Configured GitHub client instance
        """
        access_token = self.integration.get_access_token(int(installation_id)).token
        return Github(access_token)

    def update_access_token(self, new_access_token: str):
        """
        Update the stored access token.

        :param new_access_token: Fresh access token
        """
        pass
