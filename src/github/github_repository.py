from typing import Any, Dict, List

from blitzy_utils.logger import logger

from github import Github, GithubException, InputGitTreeElement
from src.api.models import (BranchRef, CommitFileInput, CommitFileOutput,
                            CommitInfo, CommitOutput, CreatePullRequestInput,
                            FileOperation)
from src.error.errors import (BranchNotFoundError, FileContentNotFoundError,
                              GithubAppException,
                              GithubRepoPRAlreadyExistError, ResourceNotFound, PRActionError)

from src.scm_base.base_classes import BaseOrganization

class GithubRepository(BaseOrganization):

    def __init__(self, github_client: Github, repo_id=None, repo_name=None):
        self.github_client = github_client

        if repo_id:
            if isinstance(repo_id, str):
                repo_id = int(repo_id)
            try:
                self.repo = github_client.get_repo(repo_id)
            except Exception as exc:
                # here we handle a specific when we can't find repo in github, we want to give user
                # a clear error
                if getattr(exc, "status", None) == 404:
                    raise ResourceNotFound(f"Repository with id {repo_id} not found in Github, check installation id") \
                        from exc
                # for any other just raise an original exception
                raise exc
        elif repo_name:
            self.repo = github_client.get_repo(repo_name)
        else:
            raise ValueError("Either repo_id or repo_name must be provided.")

        self.repo_id = repo_id
        self.repo_full_name = self.repo.full_name

    def branch_exists(self, branch_name):
        """Check if a branch exists"""
        try:
            self.repo.get_branch(branch_name)
            return True
        except GithubException as e:
            if e.status == 404:
                return False
            raise

    def get_branch_sha(self, branch_name):
        """Get the SHA of the latest commit on a branch"""
        try:
            branch = self.repo.get_branch(branch_name)
            return branch.commit.sha
        except GithubException as e:
            if e.status == 404:
                raise BranchNotFoundError(f"Branch '{branch_name}' not found in repository {self.repo_full_name}")
            raise

    def create_branch(self, new_branch_name, base_branch="main"):
        """
        Creates a new branch in the repository if it does not already exist.

        :param new_branch_name: Name of the branch to be created.
        :type new_branch_name: str
        :param base_branch: Name of the branch to base the new branch on. Defaults to "main".
        :type base_branch: str
        :return: Metadata about the created or existing branch, including name, reference,
                 commit SHA, repository information, and status.
        :rtype: dict
        """
        if self.branch_exists(new_branch_name):
            branch = self.repo.get_branch(new_branch_name)
            return {
                "name": new_branch_name,
                "ref": f"refs/heads/{new_branch_name}",
                "sha": branch.commit.sha,
                "repo_id": self.repo_id,
                "repo_name": self.repo_full_name,
                "base_branch": "unknown (existing branch)",
                "url": f"https://github.com/{self.repo_full_name}/tree/{new_branch_name}",
                "status": "existing"
            }

        base_sha = self.get_branch_sha(base_branch)
        self.repo.create_git_ref(f"refs/heads/{new_branch_name}", base_sha)
        new_branch_ref = self.repo.get_git_ref(f"heads/{new_branch_name}")

        return {
            "name": new_branch_name,
            "ref": f"refs/heads/{new_branch_name}",
            "sha": new_branch_ref.object.sha,
            "repo_id": self.repo_id,
            "repo_name": self.repo_full_name,
            "base_branch": base_branch,
            "url": f"https://github.com/{self.repo_full_name}/tree/{new_branch_name}",
            "status": "created"
        }

    def commit_changes(self, branch_name: str, commit_message: str, files: List[CommitFileInput]):
        """
        Commit changes to a specific branch in the repository.

        Creates a new commit with the specified changes on the given branch.
        It updates the branch reference and metadata with the new commit.

        :param branch_name: Name of the branch to apply the commit.
        :type branch_name: str
        :param commit_message: Commit message describing the changes.
        :type commit_message: str
        :param files: List of file changes, including their path, content, and operation type.
        :type files: List[CommitFileInput]
        :return: Information about the new commit, updated branch, and processed files.
        :rtype: CommitOutput
        :raises ValueError: If content is missing for file creation or update operation.
        """
        branch_sha = self.get_branch_sha(branch_name)
        logger.info(f"Branch with name {branch_name} found. SHA: {branch_sha}")
        logger.info(f"Committing changes to branch {branch_name} SHA: {branch_sha}")

        # Create a new tree with the changes
        logger.info(f"Fetching base tree for branch {branch_name} SHA: {branch_sha}")
        base_tree = self.repo.get_git_tree(branch_sha)
        tree_elements = []
        commit_file_outputs = []

        for file_info in files:
            path = file_info.path
            content = file_info.content
            operation = file_info.operation
            logger.info(f"Processing file {path} with operation {operation}")
            if operation == FileOperation.DELETE:
                # For deletion, use InputGitTreeElement with None as SHA
                tree_elements.append(
                    InputGitTreeElement(
                        path=path,
                        mode="100644",
                        type="blob",
                        sha=None
                    )
                )
                commit_file_outputs.append(CommitFileOutput(
                    path=path,
                    status="removed"
                ))
            else:
                # For create or update, create a new blob with the content
                if content is None:
                    raise FileContentNotFoundError(f"Content is required for {operation} operation on file {path}")

                blob = self.repo.create_git_blob(content, "utf-8")
                tree_elements.append(
                    InputGitTreeElement(
                        path=path,
                        mode="100644",
                        type="blob",
                        sha=blob.sha
                    )
                )

                status = "added" if operation == FileOperation.CREATE else "modified"
                commit_file_outputs.append(CommitFileOutput(
                    path=path,
                    status=status
                ))

        logger.info(f"Creating new tree with {len(tree_elements)} elements")
        new_tree = self.repo.create_git_tree(tree_elements, base_tree)

        logger.info("Creating new commit")
        parent = self.repo.get_git_commit(branch_sha)
        new_commit = self.repo.create_git_commit(commit_message, new_tree, [parent])

        logger.info("Updating branch reference")
        ref = self.repo.get_git_ref(f"heads/{branch_name}")
        ref.edit(new_commit.sha)

        logger.info("Updating branch metadata")
        return CommitOutput(
            commit=CommitInfo(
                sha=new_commit.sha,
                url=f"https://github.com/{self.repo_full_name}/commit/{new_commit.sha}",
                message=commit_message
            ),
            branch=BranchRef(
                name=branch_name,
                sha=new_commit.sha
            ),
            files=commit_file_outputs
        )

    def create_pull_request(self, pr_input: CreatePullRequestInput) -> Dict[str, Any]:
        """
        Creates a pull request in the repository.

        :param pr_input: Input object containing details required to create the pull request.
        :raises GithubRepoPRAlreadyExistError: If a pull request with the same base and head
            branches already exists.
        :raises GithubAppException: If there is any error while creating the pull request.
        :return: Dictionary containing raw data of the created pull request.
        """
        try:
            self.get_branch_sha(pr_input.baseBranch)
            logger.info(f"Base branch {pr_input.baseBranch} found")

            self.get_branch_sha(pr_input.headBranch)
            logger.info(f"Head branch {pr_input.headBranch} found")

            pull_request = self.repo.create_pull(
                title=pr_input.title,
                body=pr_input.description,
                head=pr_input.headBranch,
                base=pr_input.baseBranch,
                draft=pr_input.draft,
                maintainer_can_modify=pr_input.maintainerCanModify
            )
            logger.info(f"Pull request created successfully: {pull_request.id} - {pull_request.title}")
            return pull_request.raw_data

        except GithubException as e:
            if e.status == 422:
                raise GithubRepoPRAlreadyExistError(
                    f"Pull request with base branch {pr_input.baseBranch}"
                    f" and head branch {pr_input.headBranch} already exists. Skipping.")

            error_message = f"GitHub API error: {e.status} - {e.data.get('message', str(e))}"
            raise GithubAppException(error_message)
        except Exception as e:
            error_message = f"Error creating pull request: {str(e)}"
            raise GithubAppException(error_message)

    def get_default_branch(self):
        """
        Fetches the default branch of a specific repository using GitHub App installation.

        :return: The default branch name (e.g., 'main' or 'master')
        """
        try:
            return {"branch": self.repo.default_branch}
        except GithubException as e:
            logger.warning(f"GitHub API Error: {e.status} - {e.data.get('message')}")
            return None
        except Exception as e:
            logger.error(f"Error fetching default branch: {str(e)}")
            return None

    def get_branch_head_commit(self, branch_name):
        """
        Fetches information about the head commit of a specific branch in a repository for a given GitHub installation.
        Uses a GitHub App integration to authenticate and interact with the GitHub API. The function takes a repository
        identifier and branch name, retrieves the branch, and provides details about the branch"s latest commit such as
        the SHA, URL, commit message, author, and date.

        :param installation_id: The identifier of the GitHub App installation used to authenticate API requests.
        :param repo_id: The unique identifier for the repository.
        :param branch_name: The name of the branch whose head commit information needs to be fetched.
        :return: A dictionary with branch and head commit information.
        """
        try:
            branch = self.repo.get_branch(branch_name)
            return {
                "name": branch.name,
                "commit": {
                    "sha": branch.commit.sha,
                    "url": branch.commit.html_url,
                    "message": branch.commit.commit.message,
                    "author": branch.commit.commit.author.name,
                    "date": branch.commit.commit.author.date.isoformat()
                }
            }

        except Exception as e:
            logger.warning(f"Error fetching branch head commit: {str(e)}")
            return None

    def manage_pull_request(self, user_id: str, pr_number: int, action: str, merge_method: str = "merge"):
        """
        Manages a pull request in a specified GitHub repository. The method
        allows actions such as merging or closing a pull request. When merging,
        it checks if the pull request can be merged and attempts to do so using
        the specified merge method.

        Uses GitHub App credentials to authenticate and perform operations on
        a repository. Actions are performed based on the input parameters
        defining the operation and target pull request.

        :param user_id: ID of the user who initiated the operation.
        :param pr_number: Number of the pull request to manage.
        :param action: Specifies the action to perform on the pull request.
          Supported actions are "merge" or "close".
        :param merge_method: The method to use for merging the pull request.
          Required if action is "merge". Possible values are "merge", "squash", or "rebase".
        :return: A dictionary containing the success status of the operation, an explanatory
          message, and optionally additional details (e.g., merged status, SHA of the merge).
        :rtype: dict
        """
        try:
            # Get pull request
            pr = self.repo.get_pull(pr_number)

            logger.info(f"Pull request #{pr_number} found. User {user_id} initiated action '{action}'.")
            # Take action based on requested operation
            if action.lower() == "merge":
                # Check if PR can be merged
                if not pr.mergeable:
                    raise PRActionError(f"Pull request #{pr_number} cannot be merged. There may be conflicts.")

                # Merge the PR with specified method
                result = pr.merge(
                    commit_title=f"Merge pull request #{pr_number}",
                    commit_message=f"Merged via API by user with ID {user_id}",
                    merge_method=merge_method
                )

                return {
                    "success": True,
                    "message": "Pull request merged successfully",
                    "merged": result.merged,
                    "sha": result.sha
                }

            elif action.lower() == "close":
                # Close the PR without merging
                pr.edit(state="closed")

                return {
                    "success": True,
                    "message": "Pull request closed without merging"
                }
            else:
                raise PRActionError(f"Invalid action '{action}'. Use 'merge" or "close'.")
        except Exception as e:
            raise PRActionError(f"Error managing pull request: {str(e)}")
