from github import Github, GithubException
from src.error.errors import OrganizationNotFoundError
from src.scm_base.base_classes import BaseOrganization
class GitHubOrganization(BaseOrganization):
    """Class for interacting with GitHub organizations"""

    def __init__(self, github_client: Github, org_name):
        self.github_client = github_client
        self.org_name = org_name

        try:
            self.org = github_client.get_organization(org_name)
        except GithubException as e:
            if e.status == 404:
                raise OrganizationNotFoundError(f"Organization '{org_name}' not found")
            raise

    def get_repositories(self):
        """Get all repositories in the organization"""
        return list(self.org.get_repos())
    
    def get_projects(self)
        """
        Get all projects/spaces in the organization.
        For GitHub: returns empty list or organization info
        For Azure DevOps: returns actual projects
        
        :return: List of project objects
        """
        pass
