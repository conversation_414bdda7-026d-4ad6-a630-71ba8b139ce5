from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional

class BaseAppConnection(ABC):
    """Base class for platform-specific connections (GitHub App, Azure DevOps, etc.)"""
    
    @abstractmethod
    def get_client(self, identifier: str, access_token: Optional[str] = None):
        """
        Get a connection/client for the specified platform identifier.
        
        :param identifier: Platform-specific identifier (installation_id for GitHub, organization for Azure)
        :param access_token: Optional access token for authentication
        :return: Platform-specific connection/client object
        """
        pass
    
    @abstractmethod
    def update_access_token(self, new_access_token: str):
        """
        Update the stored access token.
        
        :param new_access_token: Fresh access token
        """
        pass


class BaseRepository(ABC):
    """Base class for repository operations across different Git platforms"""
    
    def __init__(self, connection, identifier: str, **kwargs):
        self.connection = connection
        self.identifier = identifier
        self.repo_id = None
        self.repo_full_name = None
    
    @abstractmethod
    def manage_pull_request(self, user_id: str, pr_identifier: int, action: str, merge_method: str = "merge"):
        """
        Manage a pull request (merge, close/abandon, etc.).
        
        :param user_id: ID of user performing the action
        :param pr_identifier: Pull request number/ID
        :param action: Action to perform ('merge', 'close', 'abandon')
        :param merge_method: Method for merging ('merge', 'squash', 'rebase')
        :return: Dictionary with operation result
        """
        pass
    
    @abstractmethod
    def create_branch(self, branch_name: str, base_branch: str = "main"):
        """
        Create a new branch.
        
        :param branch_name: Name of the new branch
        :param base_branch: Base branch to create from
        :return: Dictionary with operation result
        """
        pass


class BaseOrganization(ABC):
    """Base class for organization operations across different Git platforms"""
    
    def __init__(self, connection, org_name: str, **kwargs):
        self.connection = connection
        self.org_name = org_name
    
    @abstractmethod
    def get_repositories(self, **kwargs):
        """
        Get all repositories in the organization.
        
        :return: List of repository objects
        """
        pass
    
    @abstractmethod
    def get_projects(self):
        """
        Get all projects/spaces in the organization.
        For GitHub: returns empty list or organization info
        For Azure DevOps: returns actual projects
        
        :return: List of project objects
        """
        pass


class BaseAppService(ABC):
    """Base class for high-level application services"""
    
    def __init__(self, connection: BaseAppConnection):
        self.connection = connection
    
    @abstractmethod
    def create_branch(self, user_id: str, branch_name: str, base_branch: str = "main", **kwargs):
        """
        Create a new branch in a repository.
        
        :param user_id: ID of user creating the branch
        :param branch_name: Name of the new branch
        :param base_branch: Base branch to create from
        :return: Dictionary with operation result
        """
        pass
    
    @abstractmethod
    def manage_pull_request(self, user_id: str, pr_identifier: int, action: str, merge_method: str = "merge", **kwargs):
        """
        Manage a pull request.
        
        :param user_id: ID of user performing the action
        :param pr_identifier: Pull request number/ID
        :param action: Action to perform
        :param merge_method: Method for merging
        :return: Dictionary with operation result
        """
        pass
    
    @abstractmethod
    def get_organization(self, org_name: str, **kwargs):
        """
        Get an organization instance.
        
        :param org_name: Name of the organization
        :return: Organization instance
        """
        pass
    
    @abstractmethod
    def get_repository(self, repo_identifier: str, **kwargs):
        """
        Get a repository instance.
        
        :param repo_identifier: Repository identifier (ID, name, etc.)
        :return: Repository instance
        """
        pass
    
    def update_access_token(self, new_access_token: str):
        """
        Update the access token for the connection.
        
        :param new_access_token: Fresh access token
        """
        self.connection.update_access_token(new_access_token)
