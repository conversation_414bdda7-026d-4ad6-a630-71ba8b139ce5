from typing import Dict, Type, Any
from types import MappingProxyType

from src.github.github_app_connection import GithubAppConnection
from src.github.github_repository import GithubRepository
from src.github.github_organization import GitHubOrganization
from src.github.github_app_service import GithubAppService

from src.azure.azure_app_connection import AzureConnection
from src.azure.azure_repository import AzureRepository
from src.azure.azure_organizations import AzureOrganization
from src.azure.azure_app_service import AzureAppService
from src.scm_base.base_classes import (
    BaseAppConnection,
    BaseRepository,
    BaseOrganization,
    BaseAppService,
)
from common_models.models import VersionControlSystem

"""
Registry mapping version control system types to their corresponding connection classes.

Connection classes handle authentication and establish connections to the respective platforms.
Each platform has different authentication mechanisms:
- GitHub: Uses GitHub App installation-based authentication
- Azure DevOps: Uses Personal Access Token (PAT) or OAuth token authentication

Keys: Version control system identifiers (from VersionControlSystem enum)
Values: Connection class types that inherit from BaseAppConnection

Example:
    connection_class = SCM_CONNECTIONS[VersionControlSystem.GITHUB]
    github_connection = connection_class(app_id="123", private_key="key")
"""
SCM_CONNECTIONS: Dict[str, Type[BaseAppConnection]] = MappingProxyType(
    {
        VersionControlSystem.GITHUB: GithubAppConnection,
        VersionControlSystem.AZURE_DEVOPS: AzureConnection,
    }
)

"""
Registry mapping version control system types to their corresponding repository classes.

Repository classes provide platform-specific implementations for Git repository operations
such as pull request management, branch creation, and repository information retrieval.

Each repository class encapsulates the specific API calls and data structures
required by the respective platform while maintaining a consistent interface.

Keys: Version control system identifiers (from VersionControlSystem enum)
Values: Repository class types that inherit from BaseRepository

Example:
    repo_class = SCM_REPOSITORIES[VersionControlSystem.AZURE_DEVOPS]
    azure_repo = repo_class(connection, project_name="MyProject", repo_id="repo-123")
"""
SCM_REPOSITORIES: Dict[str, Type[BaseRepository]] = MappingProxyType(
    {
        VersionControlSystem.GITHUB: GithubRepository,
        VersionControlSystem.AZURE_DEVOPS: AzureRepository,
    }
)

"""
Registry mapping version control system types to their corresponding organization classes.

Organization classes handle operations at the organizational level, such as:
- Listing repositories within an organization
- Managing organization-level settings and permissions
- Retrieving project/workspace information

Note: Organizational structure differs between platforms:
- GitHub: Organization → Repository
- Azure DevOps: Organization → Project → Repository

Keys: Version control system identifiers (from VersionControlSystem enum)
Values: Organization class types that inherit from BaseOrganization

Example:
    org_class = SCM_ORGANIZATIONS[VersionControlSystem.GITHUB]
    github_org = org_class(connection, org_name="my-company")
"""
SCM_ORGANIZATIONS: Dict[str, Type[BaseOrganization]] = MappingProxyType(
    {
        VersionControlSystem.GITHUB: GitHubOrganization,
        VersionControlSystem.AZURE_DEVOPS: AzureOrganization,
    }
)
"""
Registry mapping version control system types to their corresponding high-level service classes.

Service classes provide the main application interface and orchestrate operations
across connections, repositories, and organizations. They offer:
- Simplified method signatures for common operations
- Automatic connection management
- Platform-agnostic interfaces for business logic

These are typically the main entry points for application code that needs
to interact with version control systems without caring about platform specifics.

Keys: Version control system identifiers (from VersionControlSystem enum)
Values: Service class types that inherit from BaseAppService

Example:
    service_class = SCM_SERVICES[VersionControlSystem.AZURE_DEVOPS]
    azure_service = service_class(access_token="pat-token-123")
    result = azure_service.manage_pull_request(user_id="user1", pr_id=456, action="merge")
"""
SCM_SERVICES: Dict[str, Type[BaseAppService]] = MappingProxyType(
    {
        VersionControlSystem.GITHUB: GithubAppService,
        VersionControlSystem.AZURE_DEVOPS: AzureAppService,
    }
)

# Complete registry for all class types
SCM_REGISTRY: Dict[str, Dict[str, Type[Any]]] = MappingProxyType(
    {
        "connection": SCM_CONNECTIONS,
        "repository": SCM_REPOSITORIES,
        "organization": SCM_ORGANIZATIONS,
        "service": SCM_SERVICES,
    }
)


class SCMFactory:
    """Factory class for creating SCM platform instances"""

    @staticmethod
    def get_connection_class(scm_type: str) -> Type[BaseAppConnection]:
        """Get the connection class for the specified SCM type"""
        
        if scm_type not in SCM_CONNECTIONS:
            raise ValueError(
                f"Unsupported SCM type: {scm_type}. Supported types: {list(SCM_CONNECTIONS.keys())}"
            )
        return SCM_CONNECTIONS[scm_type]

    @staticmethod
    def get_repository_class(scm_type: str) -> Type[BaseRepository]:
        """Get the repository class for the specified SCM type"""
   
        if scm_type not in SCM_REPOSITORIES:
            raise ValueError(
                f"Unsupported SCM type: {scm_type}. Supported types: {list(SCM_REPOSITORIES.keys())}"
            )
        return SCM_REPOSITORIES[scm_type]

    @staticmethod
    def get_organization_class(scm_type: str) -> Type[BaseOrganization]:
        """Get the organization class for the specified SCM type"""

        if scm_type not in SCM_ORGANIZATIONS:
            raise ValueError(
                f"Unsupported SCM type: {scm_type}. Supported types: {list(SCM_ORGANIZATIONS.keys())}"
            )
        return SCM_ORGANIZATIONS[scm_type]

    @staticmethod
    def get_service_class(scm_type: str) -> Type[BaseAppService]:
        """Get the service class for the specified SCM type"""
   
        if scm_type not in SCM_SERVICES:
            raise ValueError(
                f"Unsupported SCM type: {scm_type}. Supported types: {list(SCM_SERVICES.keys())}"
            )
        return SCM_SERVICES[scm_type]

    @staticmethod
    def create_connection(scm_type: str, **kwargs) -> BaseAppConnection:
        """Create a connection instance for the specified SCM type"""
        connection_class = SCMFactory.get_connection_class(scm_type)
        return connection_class(**kwargs)

    @staticmethod
    def create_repository(scm_type: str, connection, **kwargs) -> BaseRepository:
        """Create a repository instance for the specified SCM type"""
        repository_class = SCMFactory.get_repository_class(scm_type)
        return repository_class(connection, **kwargs)

    @staticmethod
    def create_organization(
        scm_type: str, connection, org_name: str, **kwargs
    ) -> BaseOrganization:
        """Create an organization instance for the specified SCM type"""
        organization_class = SCMFactory.get_organization_class(scm_type)
        return organization_class(connection, org_name, **kwargs)

    @staticmethod
    def create_service(scm_type: str, **kwargs) -> BaseAppService:
        """Create a service instance for the specified SCM type"""
        service_class = SCMFactory.get_service_class(scm_type)
        return service_class(**kwargs)

    @staticmethod
    def get_supported_scm_types() -> Dict[str, list]:
        """Get all supported SCM types by category"""
        return {
            "connection": list(SCM_CONNECTIONS.keys()),
            "repository": list(SCM_REPOSITORIES.keys()),
            "organization": list(SCM_ORGANIZATIONS.keys()),
            "service": list(SCM_SERVICES.keys()),
        }


# Usage Examples:


def example_usage():
    """Examples of how to use the SCM registry and factory"""

    # Example 1: Get class by SCM type
    github_repo_class = SCM_REPOSITORIES["github"]
    azure_service_class = SCM_SERVICES["azure"]

    # Example 2: Using the factory methods
    # Create GitHub service
    github_service = SCMFactory.create_service(
        "github", app_id="123", private_key="key"
    )

    # Create Azure service
    azure_service = SCMFactory.create_service("azure", access_token="token123")

    # Example 3: Platform-agnostic function
    def manage_pull_request_any_platform(
        scm_type: str, user_id: str, pr_id: int, **kwargs
    ):
        service = SCMFactory.create_service(scm_type, **kwargs)
        return service.manage_pull_request(user_id, pr_id, "merge")

    # Works with either platform
    github_result = manage_pull_request_any_platform(
        "github", "user1", 123, app_id="456"
    )
    azure_result = manage_pull_request_any_platform(
        "azure", "user1", 456, access_token="token"
    )

    # Example 4: Dynamic repository creation
    def get_repo_info(scm_type: str, repo_identifier: str, **kwargs):
        service = SCMFactory.create_service(scm_type, **kwargs)
        repo = service.get_repository(repo_identifier, **kwargs)
        return repo.get_repository_info()

    # Example 5: Check supported platforms
    supported = SCMFactory.get_supported_scm_types()
    print(f"Supported platforms: {supported}")


if __name__ == "__main__":
    example_usage()
