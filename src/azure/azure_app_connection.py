from msrest.authentication import BasicAuthentication

from azure.devops.connection import Connection
from src.consts import AZURE_BASE_URL
from src.scm_base.base_classes import BaseAppConnection

class AzureConnection(BaseAppConnection):
    def __init__(self, access_token=None):
        self.access_token = access_token
        self.base_url = AZURE_BASE_URL or "https://dev.azure.com"

    def get_client(self, organization, access_token=None):
        """
        Retrieves an Azure DevOps connection instance using Access Token.
        Access token expires every hour and needs to be refreshed.

        :param organization: Name of the Azure DevOps organization
        :type organization: str
        :param access_token: Optional fresh access token to use for this connection
        :type access_token: str
        :return: Configured Azure DevOps connection instance
        :rtype: Connection
        """
        # Use provided token or fall back to instance token
        token_to_use = access_token or self.access_token

        if not token_to_use:
            raise ValueError("Access token is required. Token expires every hour and must be refreshed.")

        # Create credentials using access token
        credentials = BasicAuthentication('', token_to_use)

        # Build organization URL
        organization_url = f"{self.base_url}/{organization}"

        # Create and return connection
        return Connection(base_url=organization_url, creds=credentials)

    def update_access_token(self, new_access_token):
        """
        Updates the stored access token with a fresh one.

        :param new_access_token: The new access token to use
        :type new_access_token: str
        """
        self.access_token = new_access_token
