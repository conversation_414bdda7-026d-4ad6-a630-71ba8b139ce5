
from blitzy_utils.logger import logger

from src.azure.azure_app_connection import AzureConnection
from src.azure.azure_repository import AzureRepository
from src.scm_base.base_classes import BaseAppService

class AzureAppService(BaseAppService):
    def __init__(self, access_token=None):
        self.connection = AzureConnection(access_token)
 
    def create_branch(self, user_id: str, branch_name: str, base_branch: str = "main", **kwargs)
        """
        Create a new branch in a repository.
        
        :param user_id: ID of user creating the branch
        :param branch_name: Name of the new branch
        :param base_branch: Base branch to create from
        :return: Dictionary with operation result
        """
        pass
    
    def get_organization(self, org_name: str, **kwargs)
        """
        Get an organization instance.
        
        :param org_name: Name of the organization
        :return: Organization instance
        """
        pass
    

    def get_repository(self, repo_identifier: str, **kwargs)
        """
        Get a repository instance.
        
        :param repo_identifier: Repository identifier (ID, name, etc.)
        :return: Repository instance
        """
        pass
    
    def update_access_token(self, new_access_token: str):
        """
        Update the access token for the connection.
        
        :param new_access_token: Fresh access token
        """
        self.connection.update_access_token(new_access_token)

    def create_branch(self, organization, project_name, repo_id, new_branch_name, base_branch="main", access_token=None):
        """
        Creates a new branch in the specified Azure DevOps repository.

        :param organization: The Azure DevOps organization name.
        :param project_name: The name of the project containing the repository.
        :param repo_id: The ID of the repository where the branch will be created.
        :param new_branch_name: The name of the new branch to be created.
        :param base_branch: The name of the base branch to create the new branch from,
            defaults to "main".
        :param access_token: Optional fresh access token (expires every hour).
        :return: A dictionary containing the status of the branch creation operation.
        """
        azure_connection = self.connection.get_client(organization, access_token)
        repo = AzureRepository(azure_connection, project_name, repo_id=repo_id)
        logger.info(f"Creating branch {new_branch_name} on repo {repo_id} in project {project_name}")
        output = repo.create_branch(new_branch_name, base_branch)
        logger.info(f"Branch created status: {output['status']}")
        return output

    def manage_pull_request(self, user_id: str, organization: str, project_name: str, repo_id: str,
                           pr_id: int, action: str, merge_type: str = "merge", access_token=None):
        """
        Manages a pull request in the specified Azure DevOps repository.

        :param user_id: The ID of the user initiating the operation.
        :param organization: The Azure DevOps organization name.
        :param project_name: The name of the project containing the repository.
        :param repo_id: The ID of the repository containing the pull request.
        :param pr_id: The ID of the pull request to manage.
        :param action: The action to perform ('merge' or 'abandon').
        :param merge_type: The type of merge to use ('merge', 'squash', or 'rebase').
        :param access_token: Optional fresh access token (expires every hour).
        :return: A dictionary containing the result of the pull request operation.
        """
        azure_connection = self.connection.get_client(organization, access_token)
        repo = AzureRepository(azure_connection, project_name, repo_id=repo_id)
        return repo.manage_pull_request(user_id, pr_id, action, merge_type)
