from blitzy_utils.logger import logger

from src.error.errors import PRActionError, ResourceNotFound
from src.scm_base.base_classes import BaseRepository

<<<<<<< HEAD

=======
>>>>>>> de6167b (ABK-569 Adding base class for github and azure services)
class AzureRepository(BaseRepository):

    def __init__(
        self, azure_devops_connection, project_name, repo_id=None, repo_name=None
    ):

        pass

    def create_branch(self, branch_name: str, base_branch: str = "main"):
        """
        Create a new branch.

        :param branch_name: Name of the new branch
        :param base_branch: Base branch to create from
        :return: Dictionary with operation result
        """
        pass

    def manage_pull_request(
        self, user_id: str, pr_id: int, action: str, merge_type: str = "merge"
    ):
        """
        Manages a pull request in a specified Azure DevOps repository. The method
        allows actions such as merging or abandoning a pull request. When merging,
        it checks if the pull request can be merged and attempts to do so using
        the specified merge type.

        Uses Azure DevOps credentials to authenticate and perform operations on
        a repository. Actions are performed based on the input parameters
        defining the operation and target pull request.

        :param user_id: ID of the user who initiated the operation.
        :param pr_id: ID of the pull request to manage.
        :param action: Specifies the action to perform on the pull request.
          Supported actions are "merge" or "abandon".
        :param merge_type: The type of merge to use for the pull request.
          Required if action is "merge". Possible values are "merge", "squash", or "rebase".
        :return: A dictionary containing the success status of the operation, an explanatory
          message, and optionally additional details (e.g., merge status, commit ID).
        :rtype: dict
        """
        pass
