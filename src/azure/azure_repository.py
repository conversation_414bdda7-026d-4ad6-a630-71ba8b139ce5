from blitzy_utils.logger import logger

from src.error.errors import PRActionError, ResourceNotFound
from src.scm_base.base_classes import BaseRepository

class AzureRepository(BaseRepository):

    def __init__(self, azure_devops_connection, project_name, repo_id=None, repo_name=None):
        self.connection = azure_devops_connection
        self.project = project_name
        self.git_client = self.connection.clients.get_git_client()

        if repo_id:
            if isinstance(repo_id, str):
                # Azure repo IDs are UUIDs, keep as string
                pass
            try:
                self.repo = self.git_client.get_repository(
                    repository_id=repo_id,
                    project=self.project
                )
                self.repo_id = repo_id
            except Exception as exc:
                # Handle specific case when repo is not found in Azure DevOps
                if hasattr(exc, 'status_code') and exc.status_code == 404:
                    raise ResourceNotFound(f"Repository with id {repo_id} not found in Azure DevOps project {project_name}") \
                        from exc
                # For any other exception, raise the original
                raise exc
        elif repo_name:
            try:
                self.repo = self.git_client.get_repository(
                    repository_id=repo_name,
                    project=self.project
                )
                self.repo_id = self.repo.id
            except Exception as exc:
                if hasattr(exc, 'status_code') and exc.status_code == 404:
                    raise ResourceNotFound(f"Repository '{repo_name}' not found in Azure DevOps project {project_name}") \
                        from exc
                raise exc
        else:
            raise ValueError("Either repo_id or repo_name must be provided.")

        self.repo_full_name = f"{self.project}/{self.repo.name}"

    def create_branch(self, branch_name: str, base_branch: str = "main") -> Dict[str, Any]:
        """
        Create a new branch.
        
        :param branch_name: Name of the new branch
        :param base_branch: Base branch to create from
        :return: Dictionary with operation result
        """
        pass

    def manage_pull_request(self, user_id: str, pr_id: int, action: str, merge_type: str = "merge"):
        """
        Manages a pull request in a specified Azure DevOps repository. The method
        allows actions such as merging or abandoning a pull request. When merging,
        it checks if the pull request can be merged and attempts to do so using
        the specified merge type.

        Uses Azure DevOps credentials to authenticate and perform operations on
        a repository. Actions are performed based on the input parameters
        defining the operation and target pull request.

        :param user_id: ID of the user who initiated the operation.
        :param pr_id: ID of the pull request to manage.
        :param action: Specifies the action to perform on the pull request.
          Supported actions are "merge" or "abandon".
        :param merge_type: The type of merge to use for the pull request.
          Required if action is "merge". Possible values are "merge", "squash", or "rebase".
        :return: A dictionary containing the success status of the operation, an explanatory
          message, and optionally additional details (e.g., merge status, commit ID).
        :rtype: dict
        """
        try:
            # Get pull request
            pr = self.git_client.get_pull_request(
                repository_id=self.repo_id,
                pull_request_id=pr_id,
                project=self.project
            )

            logger.info(f"Pull request #{pr_id} found. User {user_id} initiated action '{action}'.")

            # Take action based on requested operation
            if action.lower() == "merge":
                # Check if PR can be merged
                if pr.merge_status != "succeeded":
                    raise PRActionError(f"Pull request #{pr_id} cannot be merged. Status: {pr.merge_status}")

                # Prepare merge options
                merge_options = {
                    "squash_merge": merge_type.lower() == "squash",
                    "delete_source_branch": False,
                    "merge_commit_message": f"Merged via API by user with ID {user_id}",
                    "bypass_policy": False,
                    "bypass_reason": None,
                    "transition_work_items": True
                }

                if merge_type.lower() == "rebase":
                    merge_options["squash_merge"] = False
                    # Note: Azure DevOps doesn't have a direct rebase option like GitHub
                    # This would require additional logic to rebase before merge

                # Complete (merge) the PR
                completed_pr = self.git_client.update_pull_request(
                    git_pull_request_to_update={
                        "status": "completed",
                        "last_merge_source_commit": pr.last_merge_source_commit,
                        "completion_options": merge_options
                    },
                    repository_id=self.repo_id,
                    pull_request_id=pr_id,
                    project=self.project
                )

                return {
                    "success": True,
                    "message": "Pull request merged successfully",
                    "merged": completed_pr.status == "completed",
                    "merge_commit_id": completed_pr.last_merge_target_commit.commit_id if completed_pr.last_merge_target_commit else None
                }

            elif action.lower() == "abandon":
                # Abandon the PR without merging
                abandoned_pr = self.git_client.update_pull_request(
                    git_pull_request_to_update={
                        "status": "abandoned"
                    },
                    repository_id=self.repo_id,
                    pull_request_id=pr_id,
                    project=self.project
                )

                return {
                    "success": True,
                    "message": "Pull request abandoned without merging"
                }
            else:
                raise PRActionError(f"Invalid action '{action}'. Use 'merge' or 'abandon'.")

        except Exception as e:
            raise PRActionError(f"Error managing pull request: {str(e)}")
