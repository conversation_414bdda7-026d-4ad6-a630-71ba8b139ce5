from azure.devops.connection import Connection
from azure.devops.exceptions import AzureDevOpsServiceError
from src.error.errors import OrganizationNotFoundError
from src.scm_base.base_classes import BaseOrganization

<<<<<<< HEAD

=======
>>>>>>> de6167b (ABK-569 Adding base class for github and azure services)
class AzureOrganization(BaseOrganization):
    """Class for interacting with Azure DevOps organizations"""

    def __init__(self, azure_connection: Connection, org_name, project_name=None):

        pass

    def get_repositories(self, project_name=None):
        """
        Get all repositories in the organization.

        :param project_name: Optional specific project name. If not provided,
                           gets repos from all projects or the default project.
        :return: List of repository objects
        """
        pass

    def get_projects(self):
        """
        Get all projects in the organization.

        :return: List of project objects
        """
        return list(self.projects)

    def get_repositories_by_project(self):
        """
        Get repositories grouped by project.

        :return: Dictionary with project names as keys and repository lists as values
        """
        pass
