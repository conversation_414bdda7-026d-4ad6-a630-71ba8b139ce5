from azure.devops.connection import Connection
from azure.devops.exceptions import AzureDevOpsServiceError
from src.error.errors import OrganizationNotFoundError
from src.scm_base.base_classes import BaseOrganization

class AzureOrganization(BaseOrganization):
    """Class for interacting with Azure DevOps organizations"""

    def __init__(self, azure_connection: Connection, org_name, project_name=None):
        self.azure_connection = azure_connection
        self.org_name = org_name
        self.project_name = project_name
        self.git_client = azure_connection.clients.get_git_client()
        self.core_client = azure_connection.clients.get_core_client()

        try:
            # In Azure DevOps, we verify organization access by getting projects
            self.projects = self.core_client.get_projects()
            if not self.projects:
                raise OrganizationNotFoundError(f"No projects found in organization '{org_name}' or access denied")
        except AzureDevOpsServiceError as e:
            if e.status_code == 404 or e.status_code == 403:
                raise OrganizationNotFoundError(f"Organization '{org_name}' not found or access denied")
            raise

    def get_repositories(self, project_name=None):
        """
        Get all repositories in the organization.

        :param project_name: Optional specific project name. If not provided,
                           gets repos from all projects or the default project.
        :return: List of repository objects
        """
        repositories = []

        try:
            if project_name:
                # Get repositories from specific project
                repos = self.git_client.get_repositories(project=project_name)
                repositories.extend(repos)
            elif self.project_name:
                # Use default project if specified during initialization
                repos = self.git_client.get_repositories(project=self.project_name)
                repositories.extend(repos)
            else:
                # Get repositories from all projects in the organization
                for project in self.projects:
                    try:
                        repos = self.git_client.get_repositories(project=project.name)
                        repositories.extend(repos)
                    except AzureDevOpsServiceError:
                        # Skip projects where we don't have repository access
                        continue

        except AzureDevOpsServiceError as e:
            if e.status_code == 404:
                raise OrganizationNotFoundError(f"Project not found in organization '{self.org_name}'")
            raise

        return repositories

    def get_projects(self):
        """
        Get all projects in the organization.

        :return: List of project objects
        """
        return list(self.projects)

    def get_repositories_by_project(self):
        """
        Get repositories grouped by project.

        :return: Dictionary with project names as keys and repository lists as values
        """
        repos_by_project = {}

        for project in self.projects:
            try:
                repos = self.git_client.get_repositories(project=project.name)
                repos_by_project[project.name] = list(repos)
            except AzureDevOpsServiceError:
                # Skip projects where we don't have repository access
                repos_by_project[project.name] = []

        return repos_by_project
