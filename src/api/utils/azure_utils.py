from typing import Optional, Dict, Any
from azure.devops.connection import Connection
from azure.devops.exceptions import AzureDevOpsClientRequestError
from msrest.authentication import BasicAuthentication
from requests.exceptions import ConnectTimeout, ConnectionError as ConnectTimeoutError, RequestException
from urllib3.exceptions import MaxRetryError
from blitzy_utils.logger import logger
from src.consts import AZURE_BASE_URL
from src.error.errors import AzureBaseError
from blitzy_utils.common import blitzy_exponential_retry


def get_organization_info(organization_name: str, retry_state: Dict[str, int], access_token: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """
    Get Azure DevOps organization information with retry logic.
    
    :param organization_name: Name of the Azure DevOps organization
    :param retry_state: Dictionary containing retry count and max retries
    :param access_token: Optional access token (if not provided, uses env var)
    :return: Dictionary containing organization information
    """
    while retry_state["retries"] < retry_state["max"]:
        try:
            # Use provided token or fall back to environment variable
            token = access_token 
            if not token:
                raise ValueError("Access token is required for Azure DevOps authentication")
            
            # Create connection
            credentials = BasicAuthentication('', token)
            base_url = AZURE_BASE_URL or "https://dev.azure.com"
            organization_url = f"{base_url}/{organization_name}"
            
            connection = Connection(base_url=organization_url, creds=credentials)
            core_client = connection.clients.get_core_client()
            
            # Get organization and projects info
            projects = core_client.get_projects()
            
            if not projects:
                raise AzureBaseError(f"No projects found in organization '{organization_name}' or access denied")
            
            # Get organization details (Azure DevOps doesn't have a direct org info endpoint,
            # so we construct it from available information)
            return {
                organization_name: {
                    "name": organization_name,
                    "id": organization_name,  # Azure uses org name as identifier
                    "type": "Organization",
                    "url": organization_url,
                    "projects_count": len(projects),
                    "projects": [{"name": project.name, "id": str(project.id)} for project in projects[:10]]  # Limit to first 10
                }
            }
            
        except (ConnectTimeout, ConnectTimeoutError, MaxRetryError, RequestException) as e:
            logger.warning(f"Retryable Azure DevOps error: {e}")
            retry_state["retries"] += 1
            if retry_state["retries"] >= retry_state["max"]:
                raise AzureBaseError(f"Azure DevOps retry limit reached for organization {organization_name}: {str(e)}")
        
        except AzureDevOpsClientRequestError as e:
            if e.status_code == 401:
                logger.warning("Azure DevOps authentication failed")
                raise AzureBaseError("Azure DevOps authentication failed - invalid access token")
            elif e.status_code == 403:
                logger.warning("Azure DevOps access forbidden")
                raise AzureBaseError("Azure DevOps access forbidden - insufficient permissions")
            elif e.status_code == 404:
                logger.warning(f"Azure DevOps organization not found: {organization_name}")
                raise AzureBaseError(f"Azure DevOps organization '{organization_name}' not found")
            elif e.status_code == 429:
                logger.warning("Azure DevOps API rate limit exceeded")
                raise AzureBaseError("Azure DevOps API rate limit exceeded")
            else:
                error_message = f"Azure DevOps API error (status {e.status_code}): {str(e)}"
                logger.warning(error_message)
                raise AzureBaseError(error_message)
        
        except ValueError as e:
            logger.warning(f"Azure DevOps configuration error: {str(e)}")
            raise AzureBaseError(f"Azure DevOps configuration error: {str(e)}")
        
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            raise Exception(f"Unexpected error occurred: {str(e)}")


def get_organization_projects(organization_name: str, retry_state: Dict[str, int], access_token: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """
    Get detailed project information for an Azure DevOps organization.
    
    :param organization_name: Name of the Azure DevOps organization
    :param retry_state: Dictionary containing retry count and max retries  
    :param access_token: Optional access token
    :return: Dictionary containing detailed project information
    """
    while retry_state["retries"] < retry_state["max"]:
        try:
            token = access_token 
            if not token:
                raise ValueError("Access token is required for Azure DevOps authentication")
            
            credentials = BasicAuthentication('', token)
            base_url = AZURE_BASE_URL or "https://dev.azure.com"
            organization_url = f"{base_url}/{organization_name}"
            
            connection = Connection(base_url=organization_url, creds=credentials)
            core_client = connection.clients.get_core_client()
            git_client = connection.clients.get_git_client()
            
            # Get all projects
            projects = core_client.get_projects()
            
            project_details = {}
            for project in projects:
                try:
                    # Get repositories for each project
                    repos = git_client.get_repositories(project=project.name)
                    
                    project_details[project.name] = {
                        "name": project.name,
                        "id": str(project.id),
                        "description": getattr(project, 'description', ''),
                        "url": getattr(project, 'url', ''),
                        "repositories_count": len(repos),
                        "repositories": [{"name": repo.name, "id": str(repo.id)} for repo in repos[:5]]  # Limit to first 5
                    }
                except Exception:
                    # If we can't get repos for a project, still include the project info
                    project_details[project.name] = {
                        "name": project.name,
                        "id": str(project.id),
                        "description": getattr(project, 'description', ''),
                        "url": getattr(project, 'url', ''),
                        "repositories_count": 0,
                        "repositories": []
                    }
            
            return {
                "organization": organization_name,
                "projects": project_details,
                "total_projects": len(projects)
            }
            
        except (ConnectTimeout, ConnectTimeoutError, MaxRetryError, RequestException) as e:
            logger.warning(f"Retryable Azure DevOps error: {e}")
            retry_state["retries"] += 1
            if retry_state["retries"] >= retry_state["max"]:
                raise AzureBaseError(f"Azure DevOps retry limit reached for organization {organization_name}: {str(e)}")
        
        except AzureDevOpsClientRequestError as e:
            if e.status_code == 401:
                raise AzureBaseError("Azure DevOps authentication failed - invalid access token")
            elif e.status_code == 403:
                raise AzureBaseError("Azure DevOps access forbidden - insufficient permissions")
            elif e.status_code == 404:
                raise AzureBaseError(f"Azure DevOps organization '{organization_name}' not found")
            elif e.status_code == 429:
                raise AzureBaseError("Azure DevOps API rate limit exceeded")
            else:
                error_message = f"Azure DevOps API error (status {e.status_code}): {str(e)}"
                raise AzureBaseError(error_message)
        
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            raise Exception(f"Unexpected error occurred: {str(e)}")