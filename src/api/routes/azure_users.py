"""Azure DevOps users routes for archie-github-handler service."""

from typing import Any, Dict, List

from flask import Blueprint
from flask_utils.decorators import flask_pydantic_response

from src.api.models import (AzureOrgBasic, AzureOrgBasicList, Branch, BranchList,
                            Repository, RepositoryList)
from src.error.errors import ResourceNotFound
from src.service.azure_service import (get_azure_organizations_by_user,
                                       get_azure_repositories_by_user_and_org,
                                       get_azure_branches_by_repo)
from src.service.user_service import get_user_by_id

azure_users_bp = Blueprint("azure_users", __name__, url_prefix="/users")


@azure_users_bp.route("/<user_id>/organizations", methods=["GET"])
@flask_pydantic_response
def get_azure_organizations_by_user_id(user_id: str):
    """Get Azure DevOps organizations by user id."""
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")
    
    org_list = get_azure_organizations_by_user(user_info)
    response = map_azure_org_list_to_pydantic(org_list)
    return response, 200


@azure_users_bp.route("/<user_id>/organizations/<org_name>/repositories", methods=["GET"])
@flask_pydantic_response
def get_azure_repositories_by_user_id(user_id: str, org_name: str):
    """Get Azure DevOps repositories by user id and organization name."""
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    repo_list = get_azure_repositories_by_user_and_org(user_info, org_name)
    response = map_azure_repo_list_to_pydantic(repo_list)
    return response, 200


@azure_users_bp.route("/<user_id>/organizations/<org_name>/repositories/<repo_id>/branches", methods=["GET"])
@flask_pydantic_response
def get_azure_branches_by_repo_id(user_id: str, org_name: str, repo_id: str):
    """Get Azure DevOps branches by user id, organization name, and repository id."""
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    branch_list = get_azure_branches_by_repo(user_info, org_name, repo_id)
    output = map_azure_branches_to_pydantic(branch_list)
    return output, 200


def map_azure_org_list_to_pydantic(org_list: List[Dict[str, Any]]):
    """Map Azure organization list to Pydantic models."""
    mapped_list = []
    for org in org_list:
        mapped_list.append(AzureOrgBasic(**org))

    azure_org_list = AzureOrgBasicList(results=mapped_list)
    return azure_org_list


def map_azure_repo_list_to_pydantic(repo_list: List[Dict[str, Any]]):
    """Map Azure repository list to Pydantic models."""
    mapped_list = []
    for repo in repo_list:
        mapped_list.append(Repository(**repo))

    azure_repo_list = RepositoryList(results=mapped_list)
    return azure_repo_list


def map_azure_branches_to_pydantic(branches: List[Dict[str, Any]]) -> BranchList:
    """Map Azure branches to Pydantic models."""
    mapped_list = []
    for branch in branches:
        branch_obj = Branch(**branch)
        mapped_list.append(branch_obj)

    branch_list = BranchList(results=mapped_list)
    return branch_list
