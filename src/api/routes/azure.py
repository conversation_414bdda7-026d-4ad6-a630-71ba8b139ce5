import http

from blitzy_utils.logger import logger
from common_models.models import GithubInstallationStatus, VersionControlSystem
from flask import Blueprint
from flask_utils.decorators import flask_pydantic_response, validate_request

from src.api.models import AzureInstallation, SecretsOutput
from src.api.routes.azure_users import azure_users_bp
from src.api.utils.secret_manager_utils import create_or_update_azure_secret
from src.service.azure_service import get_tenant_id_from_token, perform_exchange
from src.service.git_installation_service import create_new_git_installation

azure_bp = Blueprint("azure_bp", __name__, url_prefix="/v1/azure")

# Register Azure users blueprint for organizations, projects, repositories, and branches endpoints
azure_bp.register_blueprint(azure_users_bp)


@azure_bp.route("/install", methods=["POST"])
@validate_request(AzureInstallation)
@flask_pydantic_response
def post_install(payload: AzureInstallation):
    logger.info(
        f"Handling request for a new azure installation for user {payload.user_id}"
    )
    access_token, refresh_token = perform_exchange(payload.code, payload.redirect_uri)
    logger.info("Got access and refresh tokens")
    tenant_id = get_tenant_id_from_token(access_token)
    create_or_update_azure_secret(
        access_token,
        refresh_token,
        tenant_id,
        payload.redirect_uri,
        payload.requested_scope,
    )
    logger.info(
        f"Created or updated secret for azure installation for tenant {tenant_id} and user {payload.user_id}"
    )

    installation_id = create_new_git_installation(
        user_id=payload.user_id,
        status=GithubInstallationStatus.ACTIVE,
        installation_id=tenant_id,
        target_id=tenant_id,
        target_name="",
        svc_type=VersionControlSystem.AZURE_DEVOPS,
        metadata={},
    )
    logger.info(
        f"Created new git installation {installation_id}  for tenant {tenant_id} and user {payload.user_id}"
    )
    return (
        SecretsOutput(
            accessToken=access_token,
            code=payload.code,
            installationID=tenant_id,
            setupAction="azure_setup",
        ),
        http.HTTPStatus.CREATED,
    )
