1. cd ~/work/archie-service-backend
2. virtualenv -p /usr/bin/python3.12 .venv OR just activate a venv, wherever it is (conda, etc..)

gcloud auth application-default login
3. gcloud auth login
4. gcloud config set project blitzy-os-dev

4a. Start docker:
docker run -d -p 5432:5432 	-v /Users/<USER>/Documents/blitzy/service_account_dev.json:/acct_credentials.json     gcr.io/cloud-spanner-pg-adapter/pgadapter:latest 	-p blitzy-os-dev -i blitzy-internal -d blitzy-os-db  	-c /acct_credentials.json -x

(This acts as a proxy between localhost over port 5432 and spanner db)

5. make pre-setup (Builds models.py from swagger docs)
6. make init (installs requirements.txt, etc..)

7(1). (Optionally, you can just run python main.py at this point and curl specific endpoints, but it’s best to run with a debugger)
7(2). In pycharm install envFile plugin

8. point to envfile(specific to env)
	I had to just run set -a, source envfile, set +a in terminal

9. Run as python in pycharm, not flask
10. Authethicate with postman `Firebase token`
11. Then run collectiion `Using email and password`
